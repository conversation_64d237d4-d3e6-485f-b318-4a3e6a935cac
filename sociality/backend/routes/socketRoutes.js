import express from 'express';
import { protectRoute } from '../middlewares/protectRoute.js';

const router = express.Router();

// Alternative real-time endpoint for when Socket.IO fails
router.post('/polling', protectRoute, async (req, res) => {
  try {
    const { action, data } = req.body;
    const userId = req.user._id;

    console.log(`[Polling] ${action} from user ${userId}:`, data);

    // Handle different actions
    switch (action) {
      case 'heartbeat':
        res.json({ 
          status: 'ok', 
          timestamp: new Date().toISOString(),
          userId: userId.toString()
        });
        break;

      case 'getOnlineUsers':
        // In a real implementation, you'd check a Redis cache or database
        // For now, return a simple response
        res.json({
          onlineUsers: [userId.toString()],
          lastSeenTimestamps: {},
          timestamp: new Date().toISOString()
        });
        break;

      case 'sendMessage':
        // Handle message sending via polling
        const { recipientId, message, tempId } = data;
        
        if (!recipientId || !message) {
          return res.status(400).json({ error: 'Missing recipientId or message' });
        }

        // In a real implementation, you'd save the message and notify the recipient
        console.log(`[Polling] Message from ${userId} to ${recipientId}: ${message}`);
        
        res.json({
          success: true,
          messageId: Date.now().toString(),
          tempId,
          timestamp: new Date().toISOString()
        });
        break;

      default:
        res.status(400).json({ error: 'Unknown action' });
    }
  } catch (error) {
    console.error('[Polling] Error:', error);
    res.status(500).json({ error: error.message });
  }
});

// Socket.IO status endpoint
router.get('/status', (req, res) => {
  const io = req.app.get('io');
  
  res.json({
    socketIOAvailable: !!io,
    connectedSockets: io ? io.sockets.sockets.size : 0,
    environment: process.env.NODE_ENV,
    timestamp: new Date().toISOString(),
    serverless: true,
    pollingFallbackAvailable: true
  });
});

// Test Socket.IO connection endpoint
router.post('/test-connection', protectRoute, async (req, res) => {
  try {
    const userId = req.user._id;
    const { testMessage } = req.body;

    console.log(`[Socket Test] Testing connection for user ${userId}`);

    const io = req.app.get('io');
    if (!io) {
      return res.status(500).json({ 
        error: 'Socket.IO not available',
        fallbackAvailable: true
      });
    }

    // Try to emit a test message
    io.emit('testMessage', {
      userId: userId.toString(),
      message: testMessage || 'Test message from server',
      timestamp: new Date().toISOString()
    });

    res.json({
      success: true,
      message: 'Test message sent via Socket.IO',
      connectedSockets: io.sockets.sockets.size,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('[Socket Test] Error:', error);
    res.status(500).json({ 
      error: error.message,
      fallbackAvailable: true
    });
  }
});

export default router;
